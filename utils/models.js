// 速记模型
export const NoteModel = {
  id: '', // 唯一ID
  content: '', // 内容
  priority: 0, // 优先级 0-低 1-中 2-高
  importance: 0, // 重要程度 0-低 1-中 2-高
  deadline: '', // 截止时间
  tags: [], // 标签
  repeat: {
    enabled: false, // 是否重复
    type: 'day', // day, week, month, year
    value: 1, // 间隔值
    time: '' // 提醒时间
  },
  createTime: '', // 创建时间
  updateTime: '' // 更新时间
}

// 助手模型
export const AssistantModel = {
  id: '', // 唯一ID
  name: '', // 助手名称
  avatar: '', // 头像
  prompt: '', // 提示词
  tags: [], // 关联的标签
  schedule: {
    enabled: false, // 是否定时发送
    time: '', // 发送时间
    frequency: 'day' // 频率
  },
  messages: [], // 消息历史
  unread: 0, // 未读消息数
  createTime: '', // 创建时间
  updateTime: '' // 更新时间
}

// 消息模型
export const MessageModel = {
  id: '', // 唯一ID
  content: '', // 内容
  type: 'user', // user 或 assistant
  time: '', // 发送时间
  status: 'success' // 发送状态：sending, success, failed
}