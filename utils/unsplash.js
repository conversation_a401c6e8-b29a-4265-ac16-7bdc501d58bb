// Unsplash API 封装
const API_URL = 'https://api.unsplash.com';
const ACCESS_KEY = '你的Unsplash API Key'; // 需要替换为你的API Key

export default {
  // 获取随机图片
  async getRandomPhoto(query = '', count = 1) {
    try {
      const response = await uni.request({
        url: `${API_URL}/photos/random`,
        method: 'GET',
        header: {
          'Authorization': `Client-ID ${ACCESS_KEY}`
        },
        data: {
          query,
          count
        }
      });
      
      if (response.statusCode === 