// 存储服务封装
const KEYS = {
  NOTES: 'selfgrowth_notes',
  ASSISTANTS: 'selfgrowth_assistants',
  USER: 'selfgrowth_user'
}

export default {
  // 获取数据
  get(key) {
    try {
      return uni.getStorageSync(key)
    } catch (e) {
      console.error('Storage get error:', e)
      return null
    }
  },
  
  // 保存数据
  set(key, data) {
    try {
      uni.setStorageSync(key, data)
      return true
    } catch (e) {
      console.error('Storage set error:', e)
      return false
    }
  },
  
  // 获取所有速记
  getNotes() {
    return this.get(KEYS.NOTES) || []
  },
  
  // 保存所有速记
  saveNotes(notes) {
    return this.set(KEYS.NOTES, notes)
  },
  
  // 获取所有助手
  getAssistants() {
    return this.get(KEYS.ASSISTANTS) || []
  },
  
  // 保存所有助手
  saveAssistants(assistants) {
    return this.set(KEYS.ASSISTANTS, assistants)
  },
  
  // 清除所有数据
  clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (e) {
      console.error('Storage clear error:', e)
      return false
    }
  }
}