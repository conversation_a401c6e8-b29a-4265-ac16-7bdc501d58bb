<template>
  <view class="base-card" :class="[shadow && 'base-card--shadow', `base-card--${type}`]">
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'BaseCard',
  props: {
    shadow: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
    }
  }
}
</script>

<style lang="scss" scoped>
.base-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 15px;
  
  &--shadow {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
  
  &--primary {
    border-left: 3px solid $u-primary;
  }
  
  &--success {
    border-left: 3px solid $u-success;
  }
  
  &--warning {
    border-left: 3px solid $u-warning;
  }
  
  &--error {
    border-left: 3px solid $u-error;
  }
}
</style>