<template>
  <u-tag :text="text" :type="type" :plain="plain" :size="size" />
</template>

<script>
export default {
  name: 'BaseTag',
  props: {
    text: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'primary'
    },
    plain: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'mini'
    }
  }
}
</script>