<template>
	<view class="container">
		<!-- 助手列表 -->
		<view class="assistants-section">
			<view class="section-title">我的助手</view>
			<scroll-view scroll-x class="assistants-scroll">
				<view class="assistants-list">
					<view 
						class="assistant-item" 
						v-for="(item, index) in assistants" 
						:key="index"
						@click="navigateToChat(item.id)"
					>
						<u-badge :count="item.unread" :offset="[-10, -10]" v-if="item.unread > 0">
							<u-avatar :src="item.avatar" size="60"></u-avatar>
						</u-badge>
						<u-avatar v-else :src="item.avatar" size="60"></u-avatar>
						<text class="assistant-name u-line-1">{{item.name}}</text>
						<text class="assistant-message u-line-1" v-if="item.messages.length">
							{{item.messages[item.messages.length - 1].content}}
						</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 速记列表 -->
		<view class="notes-section">
			<view class="section-title">
				<text>最近速记</text>
				<u-button type="primary" size="mini" text="查看全部" @click="navigateToTasks"></u-button>
			</view>
			
			<view class="notes-list">
				<base-card 
					v-for="(item, index) in recentNotes" 
					:key="index"
					:type="getPriorityType(item.priority)"
					@click="viewNoteDetail(item.id)"
				>
					<view class="note-content">{{item.content}}</view>
					<view class="note-footer">
						<view class="note-tags">
							<base-tag 
								v-for="(tag, tagIndex) in item.tags" 
								:key="tagIndex" 
								:text="tag"
							/>
						</view>
						<view class="note-info">
							<u-icon name="calendar" size="14"></u-icon>
							<text class="note-deadline">{{formatDate(item.deadline)}}</text>
						</view>
					</view>
				</base-card>
			</view>
			
			<view class="empty-state" v-if="recentNotes.length === 0">
				<u-empty text="暂无速记" mode="list"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
import BaseCard from '@/components/BaseCard.vue'
import BaseTag from '@/components/BaseTag.vue'
import storage from '@/utils/storage.js'
import { formatDate } from '@/utils/date.js'

export default {
	components: {
		BaseCard,
		BaseTag
	},
	data() {
		return {
			assistants: [],
			notes: []
		}
	},
	computed: {
		recentNotes() {
			return this.notes.slice(0, 5)
		}
	},
	onLoad() {
		this.loadData()
	},
	onShow() {
		this.loadData()
	},
	methods: {
		loadData() {
			this.notes = storage.getNotes()
			this.assistants = storage.getAssistants()
		},
		navigateToChat(id) {
			uni.navigateTo({
				url: `/pages/assistants/chat?id=${id}`
			})
		},
		navigateToTasks() {
			uni.switchTab({
				url: '/pages/tasks/index'
			})
		},
		viewNoteDetail(id) {
			uni.navigateTo({
				url: `/pages/tasks/detail?id=${id}`
			})
		},
		getPriorityType(priority) {
			const types = ['default', 'warning', 'error']
			return types[priority] || 'default'
		},
		formatDate
	}
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 20rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assistants-section {
  margin-bottom: 30rpx;
}

.assistants-scroll {
  white-space: nowrap;
}

.assistants-list {
  display: flex;
  padding: 10rpx 0;
}

.assistant-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
  width: 120rpx;
}

.assistant-name {
  font-size: 24rpx;
  margin-top: 10rpx;
  width: 100%;
  text-align: center;
}

.assistant-message {
  font-size: 20rpx;
  color: $u-tips-color;
  width: 100%;
  text-align: center;
}

.notes-list {
  margin-top: 20rpx;
}

.note-content {
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
}

.note-tags {
  display: flex;
  flex-wrap: wrap;
}

.note-info {
  display: flex;
  align-items: center;
  color: $u-tips-color;
}

.note-deadline {
  margin-left: 5rpx;
  font-size: 24rpx;
}

.empty-state {
  margin-top: 100rpx;
}
</style>
